# WhatsApp to Telegram Forwarder Configuration

# Telegram Bot Configuration
telegram:
  bot_token: "**********************************************"
  # channel_id: "-1003048477556" #Make Money Channel
  channel_id: "-1003065061158" #Test Channel

# WhatsApp Configuration
whatsapp:
  target_sender: "Bhushan Puri"  # Name of the sender to monitor (case-insensitive)
  scroll_up_times: 0  # How many times to scroll up on startup to load history (0 = start from latest messages only)
  poll_interval: 20  # Seconds between message checks

# File Paths
paths:
  media_dir: "whatsapp_media"
  state_file: "processed.json"
  log_file: "whatsapp2telegram.log"

# DOM Selectors for WhatsApp Web (updated for current version)
selectors:
  # Login detection selectors (try in order)
  login_indicators:
    - "[data-testid='chatlist']"
    - "div[data-testid='chatlist']"
    - "[data-testid='chat-list']"
    - "div[data-testid='chat-list']"
    - "div[aria-label*='Chat list']"
    - "div[title*='Chat list']"
    - "div[data-testid='side']"
    - "div[id='pane-side']"
    - "div._2Ts6i"  # Fallback class-based selector
    - "div[data-testid='app-wrapper-main']"
    - "div[data-testid='app']"

  # Message container selectors (try in order)
  message_containers:
    - "div[data-id]"  # Most reliable - messages have data-id
    - "[data-id]"
    - "div[data-testid='msg-container']"
    - "[data-testid='msg-container']"
    - "div.copyable-text"
    - "div[role='row']"
    - "div.message-in, div.message-out"
    - "div._2AOIt"  # Common WhatsApp message class
    - "div._1BOF7"
    - "div[data-testid='conversation-panel-messages'] > div > div"
    - "div[tabindex='-1'][role='application'] div[data-id]"

  # Chat panel selectors for scrolling
  chat_panels:
    - "div[data-testid='conversation-panel-messages']"
    - "[data-testid='conversation-panel-messages']"
    - "div[data-testid='msg-container']"
    - "div[id='main']"
    - "div._2Ts6i"
    - "div[data-testid='conversation-panel']"
    - "div[tabindex='-1'][role='application']"
    - "div[data-testid='conversation-panel-body']"

  # Media selectors
  media_elements:
    - "img[src*='blob:']"
    - "video[src*='blob:']"
    - "img"
    - "video"
    - "div[role='document']"
    - "div[data-testid='media-viewer']"
    - "span[data-testid='media-download']"
    - "div[data-testid='image-thumb']"
    - "div[data-testid='video-thumb']"

# Logging Configuration
logging:
  level: "DEBUG"  # DEBUG, INFO, WARNING, ERROR
  format: "%(asctime)s - %(levelname)s - %(message)s"
  console_output: true
  file_output: true

# Advanced Settings
advanced:
  use_system_chrome: true  # Use system Chrome browser (recommended)
  chrome_executable: "/usr/bin/google-chrome"  # Path to Chrome executable
  chrome_debugging_port: 9222  # Port for Chrome remote debugging
  browser_timeout: 30000  # Browser timeout in milliseconds
  max_retries: 3  # Maximum retries for failed operations
  retry_delay: 5  # Delay between retries in seconds
  max_file_size_mb: 2000  # Maximum file size for Telegram (2GB limit)
