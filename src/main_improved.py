import asyncio
import os
import base64
import json
import hashlib
import logging
import yaml
from pathlib import Path
from playwright.async_api import async_playwright
from telegram import Bot

# Load configuration from YAML
def load_config():
    config_path = Path(__file__).parent.parent / "config.yaml"
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    except FileNotFoundError:
        print(f"Configuration file not found: {config_path}")
        raise
    except yaml.YAMLError as e:
        print(f"Error parsing YAML configuration: {e}")
        raise

# Load configuration
config = load_config()

# Setup logging
log_level = getattr(logging, config['logging']['level'].upper())
handlers = []

if config['logging']['file_output']:
    handlers.append(logging.FileHandler(config['paths']['log_file']))
if config['logging']['console_output']:
    handlers.append(logging.StreamHandler())

logging.basicConfig(
    level=log_level,
    format=config['logging']['format'],
    handlers=handlers
)
logger = logging.getLogger(__name__)

# Extract configuration values
TARGET_SENDER = config['whatsapp']['target_sender']
TELEGRAM_BOT_TOKEN = config['telegram']['bot_token']
TELEGRAM_CHANNEL_ID = config['telegram']['channel_id']
# Ensure all paths are relative to project root
PROJECT_ROOT = Path(__file__).parent.parent
MEDIA_DIR = str(PROJECT_ROOT / config['paths']['media_dir'])
STATE_FILE = str(PROJECT_ROOT / config['paths']['state_file'])
SCROLL_UP_TIMES = config['whatsapp']['scroll_up_times']
POLL_INTERVAL = config['whatsapp']['poll_interval']
# Chrome settings
CHROME_EXECUTABLE = config['advanced']['chrome_executable']
CHROME_DEBUG_PORT = config['advanced']['chrome_debugging_port']

# DOM Selectors from config
LOGIN_SELECTORS = config['selectors']['login_indicators']
MESSAGE_SELECTORS = config['selectors']['message_containers']
CHAT_PANEL_SELECTORS = config['selectors']['chat_panels']
MEDIA_SELECTORS = config['selectors']['media_elements']

# Telegram setup
bot = Bot(token=TELEGRAM_BOT_TOKEN)

# Folders
os.makedirs(MEDIA_DIR, exist_ok=True)

# Log the paths for debugging
logger.info(f"Chrome executable: {CHROME_EXECUTABLE}")
logger.info(f"Chrome debug port: {CHROME_DEBUG_PORT}")
logger.info(f"Media directory: {MEDIA_DIR}")
logger.info(f"State file: {STATE_FILE}")


# --- Helpers ---
def load_processed_ids():
    if os.path.exists(STATE_FILE):
        with open(STATE_FILE, "r") as f:
            logger.info("Loaded processed IDs from state file.")
            return set(json.load(f))
    logger.info("No state file found. Starting fresh.")
    return set()


def save_processed_ids(processed_ids):
    with open(STATE_FILE, "w") as f:
        json.dump(list(processed_ids), f)
    logger.info("Saved processed IDs to state file.")


def make_msg_id(sender, timestamp, text, media_flag):
    """Generate unique ID by hashing sender+timestamp+text+media_flag"""
    raw = f"{sender}|{timestamp}|{text}|{media_flag}"
    return hashlib.sha1(raw.encode("utf-8")).hexdigest()


async def forward_to_telegram(message_type, content=None, media_path=None):
    """Send message/media to Telegram channel"""
    try:
        if message_type == "text" and content:
            await bot.send_message(chat_id=TELEGRAM_CHANNEL_ID, text=content)
            logger.info("Forwarded text message to Telegram.")
        elif message_type == "media" and media_path:
            # Check file size
            file_size_mb = os.path.getsize(media_path) / (1024 * 1024)
            if file_size_mb > config['advanced']['max_file_size_mb']:
                logger.warning(f"File too large ({file_size_mb:.1f}MB): {media_path}")
                return

            # Determine file type and send accordingly
            file_extension = os.path.splitext(media_path)[1].lower()

            with open(media_path, "rb") as f:
                # Check if it's an image by file extension or content
                if file_extension in ['.jpg', '.jpeg', '.png', '.gif', '.webp'] or is_image_file(media_path):
                    # Send as photo
                    await bot.send_photo(chat_id=TELEGRAM_CHANNEL_ID, photo=f)
                    logger.info(f"Forwarded image to Telegram: {media_path}")
                elif file_extension in ['.mp4', '.avi', '.mov', '.mkv', '.webm']:
                    # Send as video
                    await bot.send_video(chat_id=TELEGRAM_CHANNEL_ID, video=f)
                    logger.info(f"Forwarded video to Telegram: {media_path}")
                else:
                    # Send as document for other file types
                    await bot.send_document(chat_id=TELEGRAM_CHANNEL_ID, document=f)
                    logger.info(f"Forwarded document to Telegram: {media_path}")
    except Exception as e:
        logger.error(f"Error forwarding to Telegram: {e}")


def is_image_file(file_path):
    """Check if file is an image by reading its header"""
    try:
        with open(file_path, 'rb') as f:
            header = f.read(10)
            # Check for common image file signatures
            if header.startswith(b'\xff\xd8\xff'):  # JPEG
                return True
            elif header.startswith(b'\x89PNG\r\n\x1a\n'):  # PNG
                return True
            elif header.startswith(b'GIF8'):  # GIF
                return True
            elif header.startswith(b'RIFF') and b'WEBP' in header:  # WebP
                return True
        return False
    except:
        return False


async def extract_blob_media(media_element, filename):
    """Extract blob media (image/video/document) from WhatsApp"""
    try:
        # First, check what type of element we have and get its source
        src = await media_element.get_attribute("src")
        tag_name = await media_element.evaluate("el => el.tagName.toLowerCase()")

        logger.debug(f"Extracting media: tag={tag_name}, src={src[:100] if src else 'None'}...")

        if not src:
            logger.warning("Media element has no src attribute")
            return None

        # Try different extraction methods based on the source type
        if src.startswith("blob:"):
            # Method 1: Direct blob fetch
            try:
                blob_data = await media_element.evaluate("""async (el) => {
                    try {
                        const response = await fetch(el.src);
                        if (!response.ok) throw new Error('Fetch failed');
                        const blob = await response.blob();
                        return new Promise((resolve, reject) => {
                            const reader = new FileReader();
                            reader.onloadend = () => resolve({
                                data: reader.result,
                                type: blob.type || 'application/octet-stream'
                            });
                            reader.onerror = reject;
                            reader.readAsDataURL(blob);
                        });
                    } catch (error) {
                        return { error: error.message };
                    }
                }""")

                if 'error' in blob_data:
                    raise Exception(f"Blob fetch failed: {blob_data['error']}")

            except Exception as e:
                logger.warning(f"Direct blob fetch failed: {e}")
                # Method 2: Try canvas conversion for images
                if tag_name == "img":
                    try:
                        blob_data = await media_element.evaluate("""async (el) => {
                            return new Promise((resolve) => {
                                const canvas = document.createElement('canvas');
                                const ctx = canvas.getContext('2d');
                                canvas.width = el.naturalWidth || el.width || 300;
                                canvas.height = el.naturalHeight || el.height || 300;
                                ctx.drawImage(el, 0, 0);
                                const dataUrl = canvas.toDataURL('image/jpeg', 0.9);
                                resolve({
                                    data: dataUrl,
                                    type: 'image/jpeg'
                                });
                            });
                        }""")
                    except Exception as canvas_error:
                        logger.error(f"Canvas conversion failed: {canvas_error}")
                        return None
                else:
                    return None

        elif src.startswith("data:"):
            # Data URL - extract directly
            mime_match = src.split(';')[0].split(':')[1] if ';' in src and ':' in src else 'application/octet-stream'
            blob_data = {
                'data': src,
                'type': mime_match
            }
        else:
            logger.warning(f"Unsupported media source type: {src[:50]}...")
            return None

        # Extract the data
        data_url = blob_data['data']
        mime_type = blob_data['type'] or 'application/octet-stream'

        # Extract base64 data
        if "," in data_url:
            header, base64_data = data_url.split(",", 1)
        else:
            logger.error("Invalid data URL format")
            return None

        try:
            file_bytes = base64.b64decode(base64_data)
        except Exception as e:
            logger.error(f"Base64 decode failed: {e}")
            return None

        # Determine file extension based on MIME type
        extension = get_extension_from_mime(mime_type)
        file_path = os.path.join(MEDIA_DIR, f"{filename}{extension}")

        # Save the file
        with open(file_path, "wb") as f:
            f.write(file_bytes)

        file_size = len(file_bytes) / 1024  # Size in KB
        logger.info(f"Extracted {mime_type} media ({file_size:.1f}KB) and saved to: {file_path}")
        return file_path

    except Exception as e:
        logger.error(f"Error extracting blob media: {e}")
        return None


def get_extension_from_mime(mime_type):
    """Get file extension from MIME type"""
    mime_to_ext = {
        'image/jpeg': '.jpg',
        'image/jpg': '.jpg',
        'image/png': '.png',
        'image/gif': '.gif',
        'image/webp': '.webp',
        'video/mp4': '.mp4',
        'video/webm': '.webm',
        'video/quicktime': '.mov',
        'audio/mpeg': '.mp3',
        'audio/ogg': '.ogg',
        'application/pdf': '.pdf',
        'text/plain': '.txt'
    }
    return mime_to_ext.get(mime_type, '.bin')


async def find_element_with_selectors(page, selectors, timeout=5000):
    """Try multiple selectors and return the first one that works"""
    for selector in selectors:
        try:
            element = await page.wait_for_selector(selector, timeout=timeout)
            if element:
                logger.debug(f"Found element using selector: {selector}")
                return element, selector
        except:
            continue
    return None, None


async def scroll_up_to_load_history(page, times=5):
    """Scroll up several times to load old messages"""
    try:
        chat_panel, selector = await find_element_with_selectors(page, CHAT_PANEL_SELECTORS)
        
        if not chat_panel:
            logger.error("Chat panel not found with any selector")
            return False
            
        logger.info(f"Found chat panel using selector: {selector}")
        logger.info(f"Scrolling up {times} times to load chat history...")
        
        for i in range(times):
            await page.mouse.wheel(0, -1000)  # Scroll up
            await asyncio.sleep(2)
            logger.debug(f"Scroll {i+1}/{times} completed")
        
        logger.info(f"Successfully scrolled up {times} times to load chat history.")
        return True
    except Exception as e:
        logger.error(f"Error scrolling chat history: {e}")
        return False


async def wait_for_login(page):
    """Wait for WhatsApp Web login with better detection and error handling"""
    try:
        # Wait for page to be stable before checking login status
        await asyncio.sleep(2)

        # First check if we're already logged in
        logged_in = False
        for selector in LOGIN_SELECTORS:
            try:
                await page.wait_for_selector(selector, timeout=3000)
                logger.info(f"✅ Already logged in to WhatsApp Web (detected via {selector})")
                logged_in = True
                break
            except:
                continue

        if not logged_in:
            try:
                # Check if QR code is present
                qr_present = await page.evaluate("""() => {
                    return !!document.querySelector('canvas') ||
                           document.body.innerText.toLowerCase().includes('qr') ||
                           document.body.innerText.toLowerCase().includes('scan');
                }""")

                if qr_present:
                    logger.info("🔑 QR code detected - Please scan the QR code to log in to WhatsApp Web")
                    logger.info("📱 Open WhatsApp on your phone and scan the QR code displayed in the browser")
                else:
                    logger.info("⏳ Waiting for WhatsApp Web to load...")

            except Exception as e:
                logger.warning(f"Could not check QR code presence: {e}")
                logger.info("🔑 Please scan QR code if one is displayed")

            # Wait for any of the login indicators with longer timeout
            try:
                await page.wait_for_selector(", ".join(LOGIN_SELECTORS), timeout=300000)  # 5 minutes
                logger.info("✅ Login successful!")
            except Exception as e:
                logger.warning(f"⚠️ Login timeout or error: {e}")
                # Try to continue anyway - sometimes login works even if selectors fail
                return True

        return True
    except Exception as e:
        logger.error(f"Error during login detection: {e}")
        # Don't fail completely - try to continue
        return True


async def get_messages(page):
    """Get all messages using the best available selector, including replies"""
    # Enhanced selectors that include reply messages
    enhanced_selectors = MESSAGE_SELECTORS + [
        # Reply-specific selectors
        'div[data-id][class*="message"]',  # Messages with data-id and message class
        'div[data-id] div[class*="copyable"]',  # Copyable text within data-id containers
        'div[data-id] div[role="button"]',  # Interactive elements within messages
        '[data-testid="msg-container"] > div',  # Direct children of message containers
        'div[data-id] > div > div',  # Nested divs within message containers
        # Broader selectors for complex message structures
        'div[tabindex="-1"][data-id]',  # Focusable message elements
        'div[data-id][class*="_amjv"]',  # WhatsApp-specific message classes
    ]

    for selector in enhanced_selectors:
        try:
            messages = await page.query_selector_all(selector)
            if messages:
                logger.debug(f"Found {len(messages)} messages using selector: {selector}")
                return messages
        except Exception as e:
            logger.debug(f"Selector {selector} failed: {e}")
            continue

    # If no messages found, provide helpful diagnostic info
    diagnostic_info = await page.evaluate("""() => {
        return {
            hasMain: !!document.querySelector('#main'),
            inChat: !!document.querySelector('[data-testid*="conversation"]'),
            bodyText: document.body.innerText.substring(0, 200),
            currentUrl: window.location.href,
            dataIdCount: document.querySelectorAll('[data-id]').length,
            replyCount: document.querySelectorAll('[data-testid*="reply"]').length
        };
    }""")

    logger.debug(f"Diagnostic info: {diagnostic_info}")

    if not diagnostic_info['hasMain']:
        logger.warning("⚠️ No main chat container found - make sure you're in a chat")
    elif not diagnostic_info['inChat']:
        logger.warning("⚠️ Not in a conversation - please open a chat with messages")
    else:
        logger.warning("⚠️ No messages found with any selector - chat may be empty or selectors need updating")

    return []


async def extract_message_info(msg):
    """Extract sender, timestamp, text, and media from a message element"""
    try:
        # Initialize defaults
        sender = "Unknown"
        timestamp = "??:??"

        # Get the full message text first, but exclude media elements
        full_text = await msg.evaluate("""(element) => {
            // Clone the element to avoid modifying the original
            const clone = element.cloneNode(true);

            // Remove media elements and their artifacts from the clone
            const mediaElements = clone.querySelectorAll('img, video, canvas, [class*="media"], [class*="image"], [data-testid*="media"], [data-testid*="image"]');
            mediaElements.forEach(el => el.remove());

            // Remove elements that contain media artifacts
            const artifactElements = clone.querySelectorAll('[class*="refreshed"], [class*="tail"]');
            artifactElements.forEach(el => {
                if (el.textContent.includes('refreshed') || el.textContent.includes('tail-')) {
                    el.remove();
                }
            });

            return clone.textContent || '';
        }""")

        # Check if this is a reply message (extremely precise detection)
        is_reply = await msg.evaluate("""(element) => {
            // Only check for the most specific reply indicators
            const hasQuotedMsg = element.querySelector('[data-testid="quoted-msg"]') !== null;

            // Additional verification: check if there's actual quoted content
            const quotedElements = element.querySelectorAll('[data-testid="quoted-msg"], div[class*="quoted-msg"]');
            let hasQuotedContent = false;

            for (const elem of quotedElements) {
                if (elem.textContent && elem.textContent.trim().length > 0) {
                    hasQuotedContent = true;
                    break;
                }
            }

            // Only consider it a reply if it has both the quoted message element AND content
            return hasQuotedMsg && hasQuotedContent;
        }""")

        if is_reply:
            logger.debug(f"Detected reply message: {full_text[:100]}...")

        # Method 1: Try data-pre-plain-text attribute (most reliable)
        sender_info = await msg.get_attribute("data-pre-plain-text")
        if sender_info and "]" in sender_info and ":" in sender_info:
            # Format: "[HH:MM, DD/MM/YYYY] Sender Name:" or "[HH:MM] Sender Name:"
            try:
                bracket_end = sender_info.find("]")
                if bracket_end > 0:
                    timestamp = sender_info[1:bracket_end]  # Extract timestamp
                    remaining = sender_info[bracket_end + 1:].strip()
                    if remaining.endswith(":"):
                        sender = remaining[:-1].strip()  # Remove trailing colon
                    else:
                        sender = remaining.strip()
            except:
                pass

        # Method 2: Special handling for reply messages
        if is_reply and sender == "Unknown":
            try:
                # For replies, try to extract sender from reply structure
                reply_info = await msg.evaluate("""(element) => {
                    // Look for reply content and sender
                    const replyElements = element.querySelectorAll('[data-testid*="reply"], [class*="reply"], [class*="quoted"], [data-testid="quoted-msg"]');
                    let replySender = null;
                    let replyText = null;

                    for (const elem of replyElements) {
                        const text = elem.textContent;
                        if (text) {
                            // Try to extract sender from reply format
                            if (text.includes(':') && !text.includes('http')) {
                                const parts = text.split(':');
                                if (parts.length > 1 && parts[0].trim().length < 50) {
                                    replySender = parts[0].trim();
                                    replyText = parts.slice(1).join(':').trim();
                                    break;
                                }
                            }
                        }
                    }

                    return { sender: replySender, text: replyText };
                }""")

                if reply_info and reply_info.get('sender'):
                    sender = reply_info['sender']
                    logger.debug(f"Extracted reply sender: {sender}")

            except Exception as e:
                logger.debug(f"Error extracting reply sender: {e}")

        # Method 3: Advanced DOM structure analysis
        if sender == "Unknown" or timestamp == "??:??":
            try:
                # Check if this is an incoming or outgoing message
                is_outgoing = await msg.evaluate("""(element) => {
                    return element.classList.contains('message-out') ||
                           element.querySelector('[data-testid="tail-out"]') !== null ||
                           element.closest('[data-testid="msg-container"]')?.getAttribute('data-id')?.includes('true_') === true;
                }""")

                if is_outgoing:
                    sender = "You"
                else:
                    # Try to find sender name in various locations
                    sender_selectors = [
                        'span[dir="auto"][title]',  # Sender name with title
                        'span[dir="auto"]:first-child',  # First span in message
                        '[data-testid="conversation-info-header"] span',
                        'div[data-testid="msg-meta"] span:first-child'
                    ]

                    for selector in sender_selectors:
                        try:
                            sender_elem = await msg.query_selector(selector)
                            if sender_elem:
                                sender_text = await sender_elem.inner_text()
                                if sender_text and sender_text.strip() and not sender_text.strip().endswith("PM") and not sender_text.strip().endswith("AM"):
                                    sender = sender_text.strip()
                                    break
                        except:
                            continue

                # Extract timestamp from time elements
                time_selectors = [
                    '[data-testid="msg-time"]',
                    'span[data-testid="msg-time"]',
                    'div[data-testid="msg-meta"] span:last-child',
                    'span[title*=":"]'  # Spans with time in title
                ]

                for selector in time_selectors:
                    try:
                        time_elem = await msg.query_selector(selector)
                        if time_elem:
                            time_text = await time_elem.inner_text()
                            title_text = await time_elem.get_attribute("title")

                            # Use title if available (more reliable)
                            if title_text and (":" in title_text or "AM" in title_text or "PM" in title_text):
                                timestamp = title_text.strip()
                                break
                            elif time_text and (":" in time_text or "AM" in time_text or "PM" in time_text):
                                timestamp = time_text.strip()
                                break
                    except:
                        continue

            except Exception as e:
                logger.debug(f"Error in advanced DOM analysis: {e}")

        # Clean up the message text
        clean_text = full_text
        if clean_text:
            # Special handling for reply messages
            if is_reply:
                # Try to separate the reply content from the actual message
                try:
                    reply_parts = await msg.evaluate("""(element) => {
                        // Find the actual message content (not the quoted part)
                        const quotedElements = element.querySelectorAll('[data-testid="quoted-msg"], [class*="quoted"], [class*="reply"]');
                        let quotedText = '';

                        // Extract quoted text
                        for (const elem of quotedElements) {
                            quotedText += elem.textContent + ' ';
                        }

                        // Get the full text and try to remove quoted part
                        const fullText = element.textContent;
                        let actualMessage = fullText;

                        if (quotedText.trim()) {
                            // Remove quoted text from full text
                            actualMessage = fullText.replace(quotedText.trim(), '').trim();
                        }

                        return {
                            quoted: quotedText.trim(),
                            actual: actualMessage,
                            full: fullText
                        };
                    }""")

                    if reply_parts and reply_parts.get('actual'):
                        clean_text = reply_parts['actual']
                        logger.debug(f"Extracted reply message: quoted='{reply_parts.get('quoted', '')[:50]}...', actual='{clean_text[:50]}...'")

                except Exception as e:
                    logger.debug(f"Error processing reply text: {e}")

            # Clean up WhatsApp artifacts and timestamps
            import re

            # Remove WhatsApp UI artifacts - be extremely aggressive
            # First pass: remove common artifacts with word boundaries
            artifacts_with_boundaries = [
                r'\btail-in\b', r'\btail-out\b', r'\bmsg-dblcheck\b',
                r'\bimage-refreshed\b', r'\bvideo-refreshed\b', r'\bmedia-refreshed\b'
            ]

            for artifact in artifacts_with_boundaries:
                clean_text = re.sub(artifact, '', clean_text, flags=re.IGNORECASE)

            # Second pass: remove artifacts without word boundaries (more aggressive)
            artifacts_no_boundaries = [
                'tail-in', 'tail-out', 'image-refreshed', 'video-refreshed', 'media-refreshed',
                'forward-refreshed', 'msg-dblcheck', '_akbu', 'copyable-text'
            ]

            for artifact in artifacts_no_boundaries:
                clean_text = clean_text.replace(artifact, '')

            # Third pass: remove any remaining single character artifacts at the beginning
            clean_text = re.sub(r'^[^\w\s]+', '', clean_text)  # Remove non-word chars at start

            # Remove sender name if it appears at the beginning (for replies)
            if sender != "Unknown" and sender != "You" and clean_text.startswith(sender):
                clean_text = clean_text[len(sender):].strip()

            # Remove duplicate timestamps (pattern: HH:MM AM/PM repeated)
            # Match patterns like "1:06 PM1:06 PM" or "8:13 PM8:13 PM"
            clean_text = re.sub(r'(\d{1,2}:\d{2}\s*(?:AM|PM))(\1)', r'\1', clean_text, flags=re.IGNORECASE)

            # Remove standalone timestamps at the end
            clean_text = re.sub(r'\s*\d{1,2}:\d{2}\s*(?:AM|PM)\s*$', '', clean_text, flags=re.IGNORECASE)

            # Remove any remaining timestamps in the middle or end
            clean_text = re.sub(r'\d{1,2}:\d{2}\s*(?:AM|PM)', '', clean_text, flags=re.IGNORECASE)

            # Remove extra whitespace and clean up
            clean_text = re.sub(r'\s+', ' ', clean_text).strip()

            # Remove trailing dots if they're artifacts
            clean_text = re.sub(r'\.+$', '', clean_text).strip()

            # Remove timestamp from text if it appears at the end (fallback)
            if timestamp != "??:??" and timestamp in clean_text:
                clean_text = clean_text.replace(timestamp, "").strip()

        # Check for media with improved detection - be more specific
        media = None
        media_selectors_improved = [
            'img[src*="blob:"]',  # Blob images (most reliable)
            'video[src*="blob:"]',  # Blob videos
            '[data-testid="image-thumb"] img[src*="blob:"]',  # Image thumbnails with blob
            '[data-testid="video-thumb"] video[src*="blob:"]',  # Video thumbnails with blob
            'img[src*="data:image"]',  # Data URL images
            'img[src^="https://web.whatsapp.com"]',  # WhatsApp hosted images
        ]

        for media_selector in media_selectors_improved:
            try:
                media = await msg.query_selector(media_selector)
                if media:
                    # Verify it has a valid source and is not a UI element
                    src = await media.get_attribute("src")
                    if src and (src.startswith("blob:") or src.startswith("data:image") or "whatsapp.com" in src):
                        # Additional check: make sure it's not a tiny UI icon
                        width = await media.get_attribute("width") or "0"
                        height = await media.get_attribute("height") or "0"

                        # Skip tiny images (likely UI elements)
                        if width.isdigit() and height.isdigit():
                            if int(width) < 50 and int(height) < 50:
                                media = None
                                continue

                        break
                    else:
                        media = None
            except:
                continue

        media_flag = "1" if media else "0"

        return {
            'sender': sender,
            'timestamp': timestamp,
            'text': clean_text.strip() if clean_text else "",
            'media': media,
            'media_flag': media_flag,
            'is_reply': is_reply
        }
    except Exception as e:
        logger.error(f"Error extracting message info: {e}")
        return None


async def monitor_whatsapp():
    async with async_playwright() as p:
        # Connect to existing Chrome browser instead of launching new one
        logger.info("🌐 Connecting to system Chrome browser...")
        logger.info("💡 Make sure Chrome is running and you're logged into WhatsApp Web")

        try:
            # Try to connect to existing Chrome instance
            browser = await p.chromium.connect_over_cdp(f"http://localhost:{CHROME_DEBUG_PORT}")
            logger.info("✅ Connected to existing Chrome browser")

            # Get the default context from existing browser
            contexts = browser.contexts
            if contexts:
                context = contexts[0]
                logger.info("✅ Using existing Chrome context")
            else:
                context = await browser.new_context()
                logger.info("✅ Created new Chrome context")

        except Exception as e:
            logger.error(f"❌ Could not connect to Chrome: {e}")
            logger.error("💡 Please run './start_chrome.sh' first to start Chrome with remote debugging")
            logger.error("🔧 Make sure Chrome is running on port 9222 with remote debugging enabled")
            return

        try:
            # Get existing page or create new one
            pages = context.pages
            if pages:
                page = pages[0]
                logger.info("✅ Using existing Chrome tab")
            else:
                page = await context.new_page()
                logger.info("✅ Created new Chrome tab")

            # Navigate to WhatsApp Web if not already there
            current_url = page.url
            if "web.whatsapp.com" not in current_url:
                logger.info("🌐 Navigating to WhatsApp Web...")
                await page.goto("https://web.whatsapp.com")
            else:
                logger.info("✅ Already on WhatsApp Web")

            # Wait for page to fully load
            logger.info("⏳ Waiting for WhatsApp Web to load...")
            await asyncio.sleep(3)

            # Wait for the page to be in a stable state
            try:
                await page.wait_for_load_state('networkidle', timeout=10000)
            except:
                logger.debug("Network idle timeout - continuing anyway")

            # Check if we're logged in (no session management needed with system browser)
            login_success = await wait_for_login(page)
            if not login_success:
                logger.warning("⚠️ Please make sure you're logged into WhatsApp Web in Chrome")
                logger.info("💡 Open Chrome, go to web.whatsapp.com, and log in if needed")
                # Continue anyway - user might be logged in but selectors failed

            logger.info("✅ Using system Chrome browser - no session management needed")

            # Load previously processed IDs
            processed_ids = load_processed_ids()

            # Give user time to navigate to the target chat
            logger.info("🎯 Please navigate to the chat you want to monitor")
            logger.info(f"📝 Looking for messages from: {TARGET_SENDER}")
            logger.info("⏳ Waiting 10 seconds for you to open the target chat...")
            await asyncio.sleep(10)

            # Load older messages if scroll_up_times > 0
            if SCROLL_UP_TIMES > 0:
                logger.info(f"📜 Loading chat history (scrolling up {SCROLL_UP_TIMES} times)...")
                history_loaded = await scroll_up_to_load_history(page, SCROLL_UP_TIMES)
                if not history_loaded:
                    logger.warning("⚠️ Failed to load chat history, continuing with current messages only")
            else:
                logger.info("📱 Starting from latest messages only (no scroll configured)")

            # Main monitoring loop
            retry_count = 0
            max_retries = config['advanced']['max_retries']
            
            while True:
                try:
                    # Check if page is still valid
                    if page.is_closed():
                        logger.error("Page was closed, exiting...")
                        break
                        
                    messages = await get_messages(page)
                    
                    if not messages:
                        await asyncio.sleep(POLL_INTERVAL)
                        continue

                    for msg in messages:
                        logger.debug(f"Message found {msg}")
                        msg_info = await extract_message_info(msg)
                        # logger.info(f"Message info: {msg_info}")
                        if not msg_info:
                            continue
                            
                        # Debug: Log message info for troubleshooting
                        logger.debug(f"Message info: sender='{msg_info['sender']}', timestamp='{msg_info['timestamp']}', text='{msg_info['text'][:50]}...'")

                        # Apply sender filter (case-insensitive)
                        # If sender is Unknown, we're in a personal chat, so process all messages
                        if msg_info['sender'] == "Unknown" or TARGET_SENDER.lower() in msg_info['sender'].lower():
                            pass  # Process this message
                        else:
                            continue  # Skip other senders

                        # Generate unique ID
                        msg_id = make_msg_id(
                            msg_info['sender'],
                            msg_info['timestamp'],
                            msg_info['text'],
                            msg_info['media_flag']
                        )

                        if msg_id in processed_ids:
                            continue

                        # Print the message details
                        print("\n" + "="*60)
                        reply_indicator = "🔄 REPLY " if msg_info.get('is_reply', False) else ""
                        print(f"📱 {reply_indicator}NEW MESSAGE FROM: {msg_info['sender']}")
                        print(f"🕐 TIMESTAMP: {msg_info['timestamp']}")
                        print(f"📝 MESSAGE ID: {msg_id[:12]}...")

                        if msg_info['text']:
                            print(f"💬 TEXT: {msg_info['text']}")

                        if msg_info['media']:
                            print(f"📎 MEDIA: Yes (type: {msg_info['media_flag']})")

                        if msg_info.get('is_reply', False):
                            print("🔄 TYPE: Reply Message")

                        print("="*60)

                        # Process message
                        if msg_info['media']:
                            file_path = await extract_blob_media(msg_info['media'], f"media_{msg_id}")
                            if file_path:
                                print(f"💾 Media saved: {file_path}")
                                await forward_to_telegram("media", media_path=file_path)
                                print(f"📤 Media forwarded to Telegram")
                        elif msg_info['text']:
                            # Add 🔄 emoji for reply messages in the actual Telegram text
                            telegram_text = msg_info['text']
                            if msg_info.get('is_reply', False):
                                telegram_text = f"🔄 {telegram_text}"

                            await forward_to_telegram("text", telegram_text)
                            print(f"📤 Text forwarded to Telegram")

                        processed_ids.add(msg_id)

                    save_processed_ids(processed_ids)
                    retry_count = 0  # Reset retry count on success

                except Exception as e:
                    retry_count += 1
                    logger.error(f"Error in main loop (attempt {retry_count}/{max_retries}): {e}")
                    
                    # Check if it's a critical error that requires restart
                    if "Target page, context or browser has been closed" in str(e):
                        logger.error("Browser/page closed, exiting...")
                        break
                        
                    if retry_count >= max_retries:
                        logger.error("Max retries reached, exiting...")
                        break
                        
                    await asyncio.sleep(config['advanced']['retry_delay'])

                await asyncio.sleep(POLL_INTERVAL)
                
        except Exception as e:
            logger.error(f"Critical error in monitor_whatsapp: {e}")
        finally:
            # Cleanup
            try:
                if context:
                    await context.close()
                if browser:
                    await browser.close()
            except Exception as e:
                logger.debug(f"Cleanup completed with minor issues: {e}")


if __name__ == "__main__":
    logger.info("Starting WhatsApp to Telegram monitor...")
    logger.info(f"Target sender: {TARGET_SENDER}")
    logger.info(f"Telegram channel: {TELEGRAM_CHANNEL_ID}")
    asyncio.run(monitor_whatsapp())
